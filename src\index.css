@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');
@import "tailwindcss";

/* Base styles - Dark theme only */
* {
  border-color: #374151; /* gray-700 equivalent */
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #111827; /* gray-900 */
  color: #ffffff;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
}

/* Component styles - You can customize these with your Tailwind config */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.btn-secondary {
  @apply bg-transparent border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-300;
}

.section-padding {
  @apply py-20 px-4 sm:px-6 lg:px-8;
}

.container-custom {
  @apply max-w-7xl mx-auto;
}

.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

.card-hover {
  @apply transition-all duration-300 transform hover:scale-105 hover:shadow-xl;
}

/* Utility styles */
.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.glass-effect {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

/* Custom scrollbar - Dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-blue-500 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-blue-600;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
