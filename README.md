# Interactive Portfolio

A modern, responsive portfolio website built with React, Vite, Tailwind CSS, and enhanced with Framer Motion animations.

## 🚀 Features

- **Modern Design**: Clean, professional design with smooth animations
- **Responsive**: Fully responsive design that works on all devices
- **Interactive**: 3D background with Three.js and smooth GSAP animations
- **Performance**: Built with Vite for fast development and optimized builds
- **Dark/Light Theme Toggle**: Working theme switcher with persistence ✅
- **Accessibility**: Semantic HTML and keyboard navigation support

## 🛠️ Technologies Used

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Animations**: GSAP, Framer Motion
- **3D Graphics**: Three.js, React Three Fiber
- **Icons**: Lucide React
- **Development**: ESLint, PostCSS, Autoprefixer

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and visit `http://localhost:5173`

### Build for Production

```bash
npm run build
```

## 🎨 Customization

### Personal Information
Update the following files with your personal information:
- `src/components/Hero.jsx` - Name, title, and description
- `src/components/About.jsx` - About me content and photo
- `src/components/Contact.jsx` - Contact information
- `src/components/Footer.jsx` - Social links and contact details

### Colors and Styling
- Create your own `tailwind.config.js` to customize the color scheme
- Update `src/index.css` for custom styles
- The portfolio uses standard Tailwind classes, ready for your custom configuration

### Projects
- Update `src/components/Projects.jsx` with your actual projects
- Add project images to the `public` directory

## 📱 Responsive Design

The portfolio is fully responsive and optimized for:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 📧 Contact

- Email: <EMAIL>
- LinkedIn: [Your LinkedIn](https://linkedin.com/in/yashaswi-rai-real)
- GitHub: [Your GitHub](https://github.com/yashaswirai)

---

Made with ❤️ by [Yashaswi Rai]
